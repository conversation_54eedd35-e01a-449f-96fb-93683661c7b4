请根据以下功能反馈修复已实现的思考模型相关功能中的问题：

## 需要修复的问题

### 问题1: 思考完成后思考模块不显示
**现象**: AI思考完成后（status变为COMPLETED），思考区域完全消失，用户无法查看思考过程
**期望行为**: 思考完成后，思考区域应该保持显示，用户可以点击展开/收起查看完整的思考内容
**涉及文件**: `src/components/MsgItem/MsgItem.vue` 中的思考区域显示逻辑

### 问题2: 思考区域样式与主题不协调
**现象**: 当前思考区域的背景色过于突出，与整体界面不协调，字体颜色未使用主题推荐色
**期望行为**:
- 背景色应更接近页面背景色，降低对比度
- 字体颜色使用主题变量中的标准文字颜色
- 确保在深色/浅色主题下都有良好的视觉效果
  **涉及文件**: `src/components/MsgItem/MsgItem.vue` 中的think-block样式

### 问题3: 思考动画播放逻辑错误
**现象**: 每次接收到新的思考内容时，动画都从第一个字符重新开始播放，导致动画不连续
**期望行为**:
- 动画应该是增量式的，只对新增的内容播放打字机效果
- 已显示的内容保持静态，新内容从当前位置继续播放
- 实现真正的流式动画效果
  **涉及文件**: `src/components/MsgItem/ThinkingAnimation.vue` 中的动画逻辑

### 问题4: 重命名弹窗主题继承问题
**现象**: 重命名会话的弹窗没有正确继承naive-ui主题配置，显示为默认样式
**期望行为**:
- 弹窗应该完全继承应用的主题配置
- 确保NInput组件位于正确的主题上下文中
- 弹窗的所有元素都应该使用当前主题的颜色方案
  **涉及文件**: `src/components/ChatList/ChatList.vue` 中的弹窗实现

### 问题5: 思考时错误显示操作按钮
**现象**: AI正在思考时（status为GENERATING或WAITING），消息底部仍显示"重试"和"复制"按钮
**期望行为**:
- 思考过程中不应显示消息操作按钮
- 只有在思考和回答都完成后才显示操作按钮
- 确保UI状态与消息状态保持一致
  **涉及文件**: `src/components/MsgItem/MsgItem.vue` 中的按钮显示条件

## 修复要求
1. 逐个修复上述问题，每修复一个问题就更新开发记录
2. 确保修复后的功能通过构建测试
3. 保持现有功能的稳定性，不引入新的问题
4. 优先修复影响用户体验的关键问题（问题1和问题3）