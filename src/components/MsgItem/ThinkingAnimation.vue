<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';

const props = defineProps<{
    content: string;
    isActive: boolean; // 是否正在思考中
}>();

const displayText = ref('');
const currentIndex = ref(0);
const animationTimer = ref<number | null>(null);
const lastContentLength = ref(0);

// 智能判断是否应该显示光标
const shouldShowCursor = computed(() => {
    if (!props.isActive) return false;

    // 如果内容包含完整的思考标签，不显示光标
    const hasCompleteThinkTag = props.content.includes('</think>');
    if (hasCompleteThinkTag) {
        console.log('[ThinkingAnimation] 检测到完整标签，隐藏光标');
        return false;
    }

    // 如果动画正在播放且还有未显示的内容，显示光标
    const hasMoreContent = currentIndex.value < props.content.length;
    const isAnimating = animationTimer.value !== null;

    console.log('[ThinkingAnimation] 光标显示判断:', {
        hasCompleteThinkTag,
        hasMoreContent,
        isAnimating,
        currentIndex: currentIndex.value,
        contentLength: props.content.length
    });

    return hasMoreContent || isAnimating;
});

// 计算当前应该显示的文本
const formattedText = computed(() => {
    if (!props.isActive) {
        return props.content;
    }
    return displayText.value;
});

// 启动增量打字机动画
const startIncrementalAnimation = (fromIndex: number = 0) => {
    if (!props.isActive || !props.content) return;

    // 如果是第一次或者内容变短了，重新开始
    if (fromIndex === 0 || props.content.length < displayText.value.length) {
        displayText.value = '';
        currentIndex.value = 0;
    } else {
        // 增量模式：从当前位置继续
        currentIndex.value = fromIndex;
    }

    const typeNextChar = () => {
        if (currentIndex.value < props.content.length) {
            displayText.value = props.content.substring(0, currentIndex.value + 1);
            currentIndex.value++;
            animationTimer.value = window.setTimeout(typeNextChar, 50); // 50ms per character
        }
    };

    typeNextChar();
};

// 停止动画
const stopAnimation = () => {
    if (animationTimer.value) {
        clearTimeout(animationTimer.value);
        animationTimer.value = null;
    }
};

// 监听内容变化 - 实现增量动画
watch(() => props.content, (newContent, oldContent) => {
    if (props.isActive && newContent) {
        const oldLength = oldContent?.length || 0;
        const newLength = newContent.length;

        // 检测是否包含完整的思考结束标签
        const hasCompleteThinkTag = newContent.includes('</think>');

        console.log('[ThinkingAnimation] 内容变化:', {
            oldLength,
            newLength,
            hasCompleteThinkTag,
            currentDisplayLength: displayText.value.length
        });

        if (hasCompleteThinkTag) {
            // 如果检测到完整标签，立即显示完整内容
            console.log('[ThinkingAnimation] 检测到完整标签，立即完成动画');
            stopAnimation();
            displayText.value = newContent;
        } else if (newLength > oldLength) {
            // 内容增加，从上次的位置继续动画
            stopAnimation();
            startIncrementalAnimation(displayText.value.length);
        } else if (newLength < oldLength) {
            // 内容减少，重新开始（这种情况很少见）
            stopAnimation();
            startIncrementalAnimation(0);
        }
        // 如果长度相同，可能是内容替换，也重新开始
        else if (newContent !== oldContent) {
            stopAnimation();
            startIncrementalAnimation(0);
        }
    } else if (!props.isActive) {
        displayText.value = newContent;
    }

    lastContentLength.value = newContent.length;
}, { immediate: true });

// 监听激活状态变化
watch(() => props.isActive, (isActive) => {
    if (isActive) {
        startIncrementalAnimation(0);
    } else {
        stopAnimation();
        displayText.value = props.content;
    }
});

onMounted(() => {
    if (props.isActive) {
        startIncrementalAnimation(0);
    }
});

onUnmounted(() => {
    stopAnimation();
});
</script>

<template>
    <div class="thinking-animation">
        <div class="text-container">
            <div 
                class="animated-text"
                :class="{ 'typing': isActive }"
                v-html="formattedText"
            />
            <div
                v-if="shouldShowCursor"
                class="cursor"
            />
        </div>
    </div>
</template>

<style scoped lang="less">
.thinking-animation {
    .text-container {
        position: relative;
        display: inline-block;
        min-height: 1.2em;
        line-height: 1.5;
    }
    
    .animated-text {
        display: inline-block;
        word-wrap: break-word;
        word-break: break-word;
        white-space: pre-wrap;
        
        &.typing {
            animation: textFlow 0.3s ease-in-out;
        }
    }
    
    .cursor {
        display: inline-block;
        width: 2px;
        height: 1.2em;
        background-color: var(--primary-color, #18a058);
        margin-left: 2px;
        animation: blink 1s infinite;
        vertical-align: text-bottom;
    }
}

@keyframes textFlow {
    0% {
        opacity: 0.7;
        transform: translateY(2px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}
</style>
