<script setup lang="ts">
import { Refresh } from "@vicons/ionicons5";
import { ChevronDown, ChevronUp } from "@vicons/ionicons5";
import { Copy } from "@vicons/tabler";
import { useThemeVars, NSkeleton } from "naive-ui";
import { Msg, ImageData as MsgImageData, MessageStatus } from "@/core/chat.ts"; // Renamed ImageData to MsgImageData to avoid conflict
import { useEnvStore } from "@/store/useEnvStore.ts";
import { ref, computed, watch } from "vue";
import { useMessage } from "naive-ui";
import { contentToString, processContent } from "@/utils/messageUtils.ts";
import ImageDisplayComponent from "@/components/MsgItem/ImageDisplayComponent.vue"; // Import the new component
import MsgContent from "@/components/MsgItem/MsgContent.vue"; // Import the MsgContent component
import ThinkingAnimation from "@/components/MsgItem/ThinkingAnimation.vue"; // Import the ThinkingAnimation component
import { useLinkInterceptor } from "@/components/MsgItem/useLinkInterceptor.ts";
import { useMarkdown } from "@/components/MsgItem/useMarkdown.ts";

const message = useMessage();

const props = defineProps<{
    item: Msg;
}>();

const emit = defineEmits<{
    retry: [item: Msg];
}>();

const envStore = useEnvStore();
const themeVars = useThemeVars();
const { md } = useMarkdown(); // 使用共享的 markdown 实例
const isThinkExpanded = ref(false); // 默认不展开思考过程

// 判断思考内容是否应该显示
const shouldShowThinkContent = computed(() => {
    const hasThinkContent = !!props.item.thinkContent;
    const isThinking = isThinkingInProgress.value;
    const isExpanded = isThinkExpanded.value;
    const result = hasThinkContent && (isThinking || isExpanded);

    console.log('[MsgItem] shouldShowThinkContent计算:', {
        hasThinkContent,
        isThinking,
        isExpanded,
        result,
        messageId: props.item.id,
        status: props.item.status
    });

    return result;
});

// 判断是否应该显示骨架屏
const shouldShowSkeleton = computed(() => {
    // 只有当消息是assistant且正在等待首字符时才显示骨架屏
    // 使用status状态来判断是否正在等待首字符
    return props.item.role === "assistant" && props.item.status === MessageStatus.WAITING;
});

// 判断是否是思考模型且正在生成中
const isThinkingInProgress = computed(() => {
    return props.item.thinkContent &&
           (props.item.status === MessageStatus.GENERATING || props.item.status === MessageStatus.WAITING);
});

// 思考过程的标题文本
const thinkTitle = computed(() => {
    if (isThinkingInProgress.value) {
        return "模型正在思考中";
    }
    return "思考过程";
});

function handleRetry() {
    emit("retry", props.item);
}

function toggleThink() {
    isThinkExpanded.value = !isThinkExpanded.value;
}

function handleCopy() {
    const contentStr = contentToString(props.item.content);
    navigator.clipboard.writeText(contentStr).then(() => {
        message.success("内容已复制到剪贴板");
    });
}

// 监听思考状态变化，思考完成后自动展开
watch(() => props.item.status, (newStatus: MessageStatus, oldStatus: MessageStatus) => {
    // 当状态从GENERATING变为COMPLETED，且有思考内容时，自动展开
    if (oldStatus === MessageStatus.GENERATING &&
        newStatus === MessageStatus.COMPLETED &&
        props.item.thinkContent) {
        isThinkExpanded.value = true;
    }
});

// 设置链接拦截
const bubbleRef = ref<HTMLElement | null>(null);
useLinkInterceptor(
    bubbleRef,
    ".message-content-area",
    () => props.item.content
);
</script>

<template>
    <div
        ref="bubbleRef"
        class="gap-5 bubble max-w-full"
        :class="
            item.role === 'user'
                ? 'user pt-2 pb-2 pr-4 pl-4 bg-[#e8e8e880] dark:bg-[#323232d9] '
                : 'assistant w-full'
        "
        :style="{
            color: themeVars.textColor1,
        }"
    >
        <n-flex vertical class="content grow min-w-0">
            <div
                v-if="item.thinkContent"
                class="think-block"
            >
                <div class="think-header" @click="toggleThink">
                    <div class="think-title">{{ thinkTitle }}</div>
                    <n-icon
                        v-if="!isThinkingInProgress"
                        size="16"
                        class="toggle-icon"
                    >
                        <component
                            :is="isThinkExpanded ? ChevronUp : ChevronDown"
                        />
                    </n-icon>
                    <div
                        v-else
                        class="thinking-icon"
                    >
                        <div class="star-icon">✦</div>
                    </div>
                </div>
                <div v-show="shouldShowThinkContent">
                    <ThinkingAnimation
                        v-if="isThinkingInProgress"
                        :content="item.thinkContent || ''"
                        :is-active="true"
                    />
                    <div
                        v-else
                        v-html="md.render(item.thinkContent)"
                    />
                </div>
            </div>
            <div v-if="shouldShowSkeleton">
                <n-skeleton text :repeat="3" />
            </div>
            <!-- New rendering logic for MessageContent -->
            <MsgContent v-else :content="item.content" />
            <div
                v-if="item.role === 'assistant' && item.status === MessageStatus.COMPLETED"
                class="flex justify-between items-center"
            >
                <div class="flex gap-2">
                    <n-button text size="tiny" @click="handleRetry">
                        <n-icon>
                            <Refresh />
                        </n-icon>
                        重新生成
                    </n-button>
                    <n-button text size="tiny" @click="handleCopy">
                        <n-icon>
                            <Copy />
                        </n-icon>
                        复制
                    </n-button>
                </div>
                <div v-if="item.model" class="model-info">
                    {{ item.model }}
                </div>
            </div>
        </n-flex>
    </div>
</template>

<style scoped lang="less">
.bubble {
    margin-bottom: 24px;
    display: flex;
    align-items: flex-start;
}

.user {
    width: fit-content;
    border-radius: 12px;
    align-self: flex-end;
}

.assistant {
    border-radius: 12px;
    padding: 0;
}

.think-block {
    margin-top: 12px;
    padding: 12px 16px;
    background-color: var(--card-color);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;

    &:hover {
        background-color: var(--hover-color);
    }

    .think-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        margin-bottom: 8px;

        &:hover {
            opacity: 0.8;
        }
    }

    .think-title {
        font-weight: 500;
        color: var(--text-color-2);
        font-size: 13px;
    }

    .toggle-icon {
        color: var(--text-color-3);
        transition: all 0.2s ease;
        cursor: pointer;

        &:hover {
            color: var(--text-color-2);
        }
    }

    .thinking-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary-color);

        .star-icon {
            font-size: 14px;
            animation: rotate 2s linear infinite;
            opacity: 0.8;
        }
    }
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.model-info {
    font-size: 12px;
    color: #999;
    padding: 4px 8px;
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 4px;
}
</style>
