<script setup lang="ts">
import { computed } from "vue";
import { LayoutSidebarRight, Pencil } from "@vicons/tabler";
import { useChatStore } from "@/store/useChatStore.ts";
import { useEnvStore } from "@/store/useEnvStore.ts";
import { installUpdate } from "@/utils/updater.ts";
import ModelSwitch from "@/components/ChatHeader/ModelSwitch.vue";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();
const store = useChatStore();
const envStore = useEnvStore();
const isMsgListEmpty = computed(() => store.activeChat.msgList.length === 0);

async function handleNewChatClick() {
    // 检查当前路由是否为会话路由
    if (route.path !== "/") {
        // 如果不是会话路由，则导航回会话路由
        await router.push("/");
    }

    if (isMsgListEmpty.value) {
        return;
    }
    const chat = await store.createChat();
    store.activateChat(chat.id);
}

async function toggleCollapse() {
    // 检查当前路由是否为会话路由
    if (route.path !== "/") {
        // 如果不是会话路由，则导航回会话路由
        await router.push("/");
    }
    store.isSidebarCollapsed = !store.isSidebarCollapsed;
}

function handleBack() {
    router.push("/");
}

async function handleUpgradeClick() {
    try {
        await installUpdate();
    } catch (error) {
        console.error("Failed to install update:", error);
        // 这里可以添加用户通知
    }
}
</script>

<template>
    <div
        class="app-header flex items-center justify-between h-14 pl-2"
        :class="{
            'pl-38':
                store.isSidebarCollapsed &&
                !envStore.isWin &&
                route.path === '/',
            'pl-20':
                store.isSidebarCollapsed &&
                envStore.isWin &&
                route.path === '/',
            'pl-18': !envStore.isWin && route.path === '/setting',
        }"
        data-tauri-drag-region
    >
        <!-- 手机版 -->
        <!-- <div class="flex md:hidden title w-fit">
            <n-icon
                size="24"
                class="icon"
                @click="toggleCollapse"
                v-if="store.isSidebarCollapsed"
            >
                <layout-sidebar-right />
            </n-icon>
            <span class="grow"></span>
            <model-switch />
            <span class="grow"></span>
            <n-icon
                size="24"
                class="icon"
                @click="handleNewChatClick"
                v-if="store.isSidebarCollapsed"
            >
                <pencil />
            </n-icon>
        </div> -->
        <!-- 有两种情况，会话页面展示图标和模型选择，设置页面只展示文字标题 -->
        <div
            v-if="route.path === '/'"
            class="flex title w-full justify-between"
            data-tauri-drag-region
        >
            <div
                class="flex items-center"
                :class="{
                    'fixed left-2': envStore.isWin,
                    'fixed left-20': !envStore.isWin,
                }"
            >
                <n-icon
                    size="24"
                    class="icon cursor-pointer"
                    @click="toggleCollapse"
                >
                    <layout-sidebar-right />
                </n-icon>
                <n-icon
                    size="24"
                    class="icon cursor-pointer"
                    @click="handleNewChatClick"
                >
                    <pencil />
                </n-icon>
            </div>
            <model-switch />
            <div class="flex items-center gap-2">
                <n-button
                    v-if="envStore.updateDownloaded && envStore.isDesktop"
                    type="primary"
                    size="small"
                    @click="handleUpgradeClick"
                    :loading="envStore.updateInstalling"
                    :disabled="envStore.updateInstalling"
                    class="upgrade-btn"
                >
                    {{ envStore.updateInstalling ? "升级中..." : "升级" }}
                </n-button>
            </div>
        </div>
        <div
            v-if="route.path === '/setting'"
            class="flex items-center justify-between title w-full pl-4 pr-4"
            data-tauri-drag-region
        >
            <p
                class="text-xl font-bold text-gray-800 dark:text-white pointer-events-none"
            >
                设置
            </p>
            <div class="flex items-center gap-2">
                <n-button
                    v-if="envStore.updateDownloaded && envStore.isDesktop"
                    type="primary"
                    size="small"
                    @click="handleUpgradeClick"
                    :loading="envStore.updateInstalling"
                    :disabled="envStore.updateInstalling"
                    class="upgrade-btn"
                >
                    {{ envStore.updateInstalling ? "升级中..." : "升级" }}
                </n-button>
                <n-button @click="handleBack">返回</n-button>
            </div>
        </div>
    </div>
</template>

<style scoped lang="less">
.app-header {
    width: 100%;
    position: relative;
    background-color: transparent;
    border-bottom: solid 1px var(--border-color-primary);
    transition: 0.3s;

    .title {
        font-size: 16px;
        font-weight: 600;
        align-items: center;
        gap: 8px;
        color: var(--text-secondary-color);
    }

    .icon {
        box-sizing: content-box;
        cursor: pointer;
        padding: 6px;
        border-radius: 9px;
        transition: background-color 0.3s;
        color: var(--icon-color);

        @media screen and (min-width: 768px) {
            &:hover {
                background-color: var(--icon-hover-color);
            }
        }
    }

    .upgrade-btn {
        margin-left: 8px;
        margin-right: 8px;
        font-size: 12px;
        height: 28px;
        padding: 0 12px;
        border-radius: 14px;
        font-weight: 500;
    }
}
</style>
